# 生产环境配置
spring:
  profiles:
    active: prod

# 生产环境日志配置
logging:
  level:
    # MyBatis SQL日志 - 生产环境只记录重要信息
    com.example.meals.mapper: INFO
    # 根日志级别
    root: WARN
    # Spring框架日志
    org.springframework: WARN
    # 数据库连接池日志
    com.zaxxer.hikari: WARN
    # 应用程序日志
    com.example.meals: INFO
  
  # 日志文件配置
  file:
    # 日志文件路径
    path: /var/log/meals-app
  
  # 日志输出格式
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"

# MyBatis配置
mybatis:
  configuration:
    # 生产环境使用SLF4J
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl

# 性能优化配置
server:
  # 启用压缩
  compression:
    enabled: true
  # 连接超时
  connection-timeout: 20000
