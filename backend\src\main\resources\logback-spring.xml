<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 定义日志文件存储路径 -->
    <property name="LOG_PATH" value="src/main/resources/logs"/>
    
    <!-- 定义日志输出格式 -->
    <property name="CONSOLE_LOG_PATTERN"
              value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%15.15t] %-5level %-40.40logger{39} : %m%n"/>
    <property name="FILE_LOG_PATTERN"
              value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"/>

    <!-- 控制台输出配置 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <withJansi>true</withJansi>
    </appender>
    
    <!-- My<PERSON>atis SQL日志文件输出配置 -->
    <appender name="MYBATIS_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 当前日志文件名 -->
        <file>${LOG_PATH}/mybatis-current.log</file>
        
        <!-- 日志轮转策略 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 按月轮转的文件名模式 -->
            <fileNamePattern>${LOG_PATH}/mybatis-%d{yyyy-MM}.log</fileNamePattern>
            <!-- 保留12个月的日志文件 -->
            <maxHistory>12</maxHistory>
            <!-- 总日志文件大小限制（可选） -->
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
        
        <!-- 日志输出格式 -->
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        
        <!-- 日志级别过滤器 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>DEBUG</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>NEUTRAL</onMismatch>
        </filter>
    </appender>
    
    <!-- 应用程序日志文件输出配置 -->
    <appender name="APP_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/application-current.log</file>
        
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/application-%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
            <totalSizeCap>500MB</totalSizeCap>
        </rollingPolicy>
        
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        
        <!-- 只记录INFO及以上级别的日志 -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
    </appender>
    
    <!-- MyBatis Mapper日志配置 - 专门用于SQL日志 -->
    <logger name="com.example.meals.mapper" level="DEBUG" additivity="false">
        <appender-ref ref="MYBATIS_FILE"/>
        <appender-ref ref="CONSOLE"/>
    </logger>
    
    <!-- 数据库连接池日志 -->
    <logger name="com.zaxxer.hikari" level="INFO" additivity="false">
        <appender-ref ref="APP_FILE"/>
        <appender-ref ref="CONSOLE"/>
    </logger>
    
    <!-- Spring框架日志 -->
    <logger name="org.springframework" level="INFO" additivity="false">
        <appender-ref ref="APP_FILE"/>
        <appender-ref ref="CONSOLE"/>
    </logger>
    
    <!-- 根日志配置 -->
    <root level="INFO">
        <appender-ref ref="APP_FILE"/>
        <appender-ref ref="CONSOLE"/>
    </root>
    
    <!-- 开发环境配置 -->
    <springProfile name="dev">
        <!-- 开发环境下，MyBatis日志同时输出到控制台和文件 -->
        <logger name="com.example.meals.mapper" level="DEBUG" additivity="false">
            <appender-ref ref="MYBATIS_FILE"/>
            <appender-ref ref="CONSOLE"/>
        </logger>
    </springProfile>
    
    <!-- 生产环境配置 -->
    <springProfile name="prod">
        <!-- 生产环境下，MyBatis日志只输出到文件，减少控制台输出 -->
        <logger name="com.example.meals.mapper" level="INFO" additivity="false">
            <appender-ref ref="MYBATIS_FILE"/>
        </logger>
        
        <!-- 生产环境关闭控制台输出 -->
        <root level="WARN">
            <appender-ref ref="APP_FILE"/>
        </root>
    </springProfile>
    
</configuration>
