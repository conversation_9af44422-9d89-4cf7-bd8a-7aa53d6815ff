package com.example.meals.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 日志配置类
 * 负责初始化日志目录和验证日志配置
 */
@Configuration
public class LoggingConfig {
    
    private static final Logger logger = LoggerFactory.getLogger(LoggingConfig.class);
    
    /**
     * 应用启动时初始化日志目录
     */
    @Bean
    public CommandLineRunner initializeLogging(Environment environment) {
        return args -> {
            try {
                // 获取当前激活的配置文件
                String[] activeProfiles = environment.getActiveProfiles();
                String profile = activeProfiles.length > 0 ? activeProfiles[0] : "default";
                
                logger.info("当前激活的配置文件: {}", profile);
                
                // 创建日志目录
                String logPath = "src/main/resources/logs";
                if ("prod".equals(profile)) {
                    logPath = "/var/log/meals-app";
                }
                
                Path logsDir = Paths.get(logPath);
                if (!Files.exists(logsDir)) {
                    Files.createDirectories(logsDir);
                    logger.info("创建日志目录: {}", logsDir.toAbsolutePath());
                } else {
                    logger.info("日志目录已存在: {}", logsDir.toAbsolutePath());
                }
                
                // 验证日志目录权限
                File logDirFile = logsDir.toFile();
                if (!logDirFile.canWrite()) {
                    logger.warn("日志目录没有写权限: {}", logsDir.toAbsolutePath());
                } else {
                    logger.info("日志目录权限验证通过: {}", logsDir.toAbsolutePath());
                }
                
                // 输出MyBatis日志配置信息
                logger.info("MyBatis SQL日志已配置，日志文件将保存到: {}", logsDir.toAbsolutePath());
                logger.info("日志文件命名格式: mybatis-YYYY-MM.log");
                logger.info("当前环境MyBatis日志级别: {}", 
                    "dev".equals(profile) ? "DEBUG" : "INFO");
                
            } catch (Exception e) {
                logger.error("初始化日志配置失败", e);
            }
        };
    }
}
