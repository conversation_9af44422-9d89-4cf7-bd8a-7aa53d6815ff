2025-08-14 19:07:10.576 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-08-14 19:07:10.606 [main] INFO  com.example.meals.MealsApplication - Starting MealsApplication using Java 21.0.8 with PID 12688 (C:\Users\<USER>\Desktop\meals\backend\target\classes started by 21653 in C:\Users\<USER>\Desktop\meals\backend)
2025-08-14 19:07:10.607 [main] INFO  com.example.meals.MealsApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-14 19:07:11.455 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-14 19:07:11.465 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-14 19:07:11.467 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-14 19:07:11.467 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-08-14 19:07:11.499 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-14 19:07:11.499 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 853 ms
2025-08-14 19:07:11.855 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@5bec3e0, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3b57dba4, org.springframework.security.web.context.SecurityContextHolderFilter@20bb85b4, org.springframework.security.web.header.HeaderWriterFilter@71c0b742, org.springframework.security.web.authentication.logout.LogoutFilter@67d8faec, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3be3e76c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6c07ad6b, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@35f7969d, org.springframework.security.web.session.SessionManagementFilter@4703c998, org.springframework.security.web.access.ExceptionTranslationFilter@2abc55c4, org.springframework.security.web.access.intercept.AuthorizationFilter@6b2aafbc]
2025-08-14 19:07:12.084 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-14 19:07:12.095 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path ''
2025-08-14 19:07:12.100 [main] INFO  com.example.meals.MealsApplication - Started MealsApplication in 1.976 seconds (process running for 2.637)
2025-08-14 19:09:07.421 [main] ERROR org.springframework.boot.SpringApplication - Application run failed
java.lang.IllegalStateException: java.lang.IllegalStateException: Logback configuration error detected: 
ERROR in ch.qos.logback.core.pattern.parser.Compiler@1ca25c47 - There is no conversion class registered for composite conversion word [clr]
ERROR in ch.qos.logback.core.pattern.parser.Compiler@1ca25c47 - Failed to create converter for [%clr] keyword
ERROR in ch.qos.logback.core.pattern.parser.Compiler@1ca25c47 - There is no conversion class registered for composite conversion word [clr]
ERROR in ch.qos.logback.core.pattern.parser.Compiler@1ca25c47 - Failed to create converter for [%clr] keyword
ERROR in ch.qos.logback.core.pattern.parser.Compiler@1ca25c47 - There is no conversion class registered for composite conversion word [clr]
ERROR in ch.qos.logback.core.pattern.parser.Compiler@1ca25c47 - Failed to create converter for [%clr] keyword
ERROR in ch.qos.logback.core.pattern.parser.Compiler@1ca25c47 - There is no conversion class registered for composite conversion word [clr]
ERROR in ch.qos.logback.core.pattern.parser.Compiler@1ca25c47 - Failed to create converter for [%clr] keyword
ERROR in ch.qos.logback.core.pattern.parser.Compiler@1ca25c47 - There is no conversion class registered for composite conversion word [clr]
ERROR in ch.qos.logback.core.pattern.parser.Compiler@1ca25c47 - Failed to create converter for [%clr] keyword
	at org.springframework.boot.context.logging.LoggingApplicationListener.initializeSystem(LoggingApplicationListener.java:347)
	at org.springframework.boot.context.logging.LoggingApplicationListener.initialize(LoggingApplicationListener.java:298)
	at org.springframework.boot.context.logging.LoggingApplicationListener.onApplicationEnvironmentPreparedEvent(LoggingApplicationListener.java:246)
	at org.springframework.boot.context.logging.LoggingApplicationListener.onApplicationEvent(LoggingApplicationListener.java:223)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:149)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:137)
	at org.springframework.boot.context.event.EventPublishingRunListener.multicastInitialEvent(EventPublishingRunListener.java:136)
	at org.springframework.boot.context.event.EventPublishingRunListener.environmentPrepared(EventPublishingRunListener.java:81)
	at org.springframework.boot.SpringApplicationRunListeners.lambda$environmentPrepared$2(SpringApplicationRunListeners.java:64)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:118)
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:112)
	at org.springframework.boot.SpringApplicationRunListeners.environmentPrepared(SpringApplicationRunListeners.java:63)
	at org.springframework.boot.SpringApplication.prepareEnvironment(SpringApplication.java:366)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1342)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1331)
	at com.example.meals.MealsApplication.main(MealsApplication.java:18)
Caused by: java.lang.IllegalStateException: Logback configuration error detected: 
ERROR in ch.qos.logback.core.pattern.parser.Compiler@1ca25c47 - There is no conversion class registered for composite conversion word [clr]
ERROR in ch.qos.logback.core.pattern.parser.Compiler@1ca25c47 - Failed to create converter for [%clr] keyword
ERROR in ch.qos.logback.core.pattern.parser.Compiler@1ca25c47 - There is no conversion class registered for composite conversion word [clr]
ERROR in ch.qos.logback.core.pattern.parser.Compiler@1ca25c47 - Failed to create converter for [%clr] keyword
ERROR in ch.qos.logback.core.pattern.parser.Compiler@1ca25c47 - There is no conversion class registered for composite conversion word [clr]
ERROR in ch.qos.logback.core.pattern.parser.Compiler@1ca25c47 - Failed to create converter for [%clr] keyword
ERROR in ch.qos.logback.core.pattern.parser.Compiler@1ca25c47 - There is no conversion class registered for composite conversion word [clr]
ERROR in ch.qos.logback.core.pattern.parser.Compiler@1ca25c47 - Failed to create converter for [%clr] keyword
ERROR in ch.qos.logback.core.pattern.parser.Compiler@1ca25c47 - There is no conversion class registered for composite conversion word [clr]
ERROR in ch.qos.logback.core.pattern.parser.Compiler@1ca25c47 - Failed to create converter for [%clr] keyword
	at org.springframework.boot.logging.logback.LogbackLoggingSystem.reportConfigurationErrorsIfNecessary(LogbackLoggingSystem.java:277)
	at org.springframework.boot.logging.logback.LogbackLoggingSystem.loadConfiguration(LogbackLoggingSystem.java:255)
	at org.springframework.boot.logging.AbstractLoggingSystem.initializeWithConventions(AbstractLoggingSystem.java:81)
	at org.springframework.boot.logging.AbstractLoggingSystem.initialize(AbstractLoggingSystem.java:61)
	at org.springframework.boot.logging.logback.LogbackLoggingSystem.initialize(LogbackLoggingSystem.java:189)
	at org.springframework.boot.context.logging.LoggingApplicationListener.initializeSystem(LoggingApplicationListener.java:332)
	... 19 common frames omitted
2025-08-14 19:09:33.231 [main] ERROR org.springframework.boot.SpringApplication - Application run failed
java.lang.IllegalStateException: java.lang.IllegalStateException: Logback configuration error detected: 
ERROR in ch.qos.logback.core.pattern.parser.Compiler@69653e16 - There is no conversion class registered for composite conversion word [clr]
ERROR in ch.qos.logback.core.pattern.parser.Compiler@69653e16 - Failed to create converter for [%clr] keyword
ERROR in ch.qos.logback.core.pattern.parser.Compiler@69653e16 - There is no conversion class registered for composite conversion word [clr]
ERROR in ch.qos.logback.core.pattern.parser.Compiler@69653e16 - Failed to create converter for [%clr] keyword
ERROR in ch.qos.logback.core.pattern.parser.Compiler@69653e16 - There is no conversion class registered for composite conversion word [clr]
ERROR in ch.qos.logback.core.pattern.parser.Compiler@69653e16 - Failed to create converter for [%clr] keyword
ERROR in ch.qos.logback.core.pattern.parser.Compiler@69653e16 - There is no conversion class registered for composite conversion word [clr]
ERROR in ch.qos.logback.core.pattern.parser.Compiler@69653e16 - Failed to create converter for [%clr] keyword
ERROR in ch.qos.logback.core.pattern.parser.Compiler@69653e16 - There is no conversion class registered for composite conversion word [clr]
ERROR in ch.qos.logback.core.pattern.parser.Compiler@69653e16 - Failed to create converter for [%clr] keyword
	at org.springframework.boot.context.logging.LoggingApplicationListener.initializeSystem(LoggingApplicationListener.java:347)
	at org.springframework.boot.context.logging.LoggingApplicationListener.initialize(LoggingApplicationListener.java:298)
	at org.springframework.boot.context.logging.LoggingApplicationListener.onApplicationEnvironmentPreparedEvent(LoggingApplicationListener.java:246)
	at org.springframework.boot.context.logging.LoggingApplicationListener.onApplicationEvent(LoggingApplicationListener.java:223)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:149)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:137)
	at org.springframework.boot.context.event.EventPublishingRunListener.multicastInitialEvent(EventPublishingRunListener.java:136)
	at org.springframework.boot.context.event.EventPublishingRunListener.environmentPrepared(EventPublishingRunListener.java:81)
	at org.springframework.boot.SpringApplicationRunListeners.lambda$environmentPrepared$2(SpringApplicationRunListeners.java:64)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:118)
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:112)
	at org.springframework.boot.SpringApplicationRunListeners.environmentPrepared(SpringApplicationRunListeners.java:63)
	at org.springframework.boot.SpringApplication.prepareEnvironment(SpringApplication.java:366)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1342)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1331)
	at com.example.meals.MealsApplication.main(MealsApplication.java:18)
Caused by: java.lang.IllegalStateException: Logback configuration error detected: 
ERROR in ch.qos.logback.core.pattern.parser.Compiler@69653e16 - There is no conversion class registered for composite conversion word [clr]
ERROR in ch.qos.logback.core.pattern.parser.Compiler@69653e16 - Failed to create converter for [%clr] keyword
ERROR in ch.qos.logback.core.pattern.parser.Compiler@69653e16 - There is no conversion class registered for composite conversion word [clr]
ERROR in ch.qos.logback.core.pattern.parser.Compiler@69653e16 - Failed to create converter for [%clr] keyword
ERROR in ch.qos.logback.core.pattern.parser.Compiler@69653e16 - There is no conversion class registered for composite conversion word [clr]
ERROR in ch.qos.logback.core.pattern.parser.Compiler@69653e16 - Failed to create converter for [%clr] keyword
ERROR in ch.qos.logback.core.pattern.parser.Compiler@69653e16 - There is no conversion class registered for composite conversion word [clr]
ERROR in ch.qos.logback.core.pattern.parser.Compiler@69653e16 - Failed to create converter for [%clr] keyword
ERROR in ch.qos.logback.core.pattern.parser.Compiler@69653e16 - There is no conversion class registered for composite conversion word [clr]
ERROR in ch.qos.logback.core.pattern.parser.Compiler@69653e16 - Failed to create converter for [%clr] keyword
	at org.springframework.boot.logging.logback.LogbackLoggingSystem.reportConfigurationErrorsIfNecessary(LogbackLoggingSystem.java:277)
	at org.springframework.boot.logging.logback.LogbackLoggingSystem.loadConfiguration(LogbackLoggingSystem.java:255)
	at org.springframework.boot.logging.AbstractLoggingSystem.initializeWithConventions(AbstractLoggingSystem.java:81)
	at org.springframework.boot.logging.AbstractLoggingSystem.initialize(AbstractLoggingSystem.java:61)
	at org.springframework.boot.logging.logback.LogbackLoggingSystem.initialize(LogbackLoggingSystem.java:189)
	at org.springframework.boot.context.logging.LoggingApplicationListener.initializeSystem(LoggingApplicationListener.java:332)
	... 19 common frames omitted
2025-08-14 19:11:38.702 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-08-14 19:11:38.731 [main] INFO  com.example.meals.MealsApplication - Starting MealsApplication using Java 21.0.8 with PID 16256 (C:\Users\<USER>\Desktop\meals\backend\target\classes started by 21653 in C:\Users\<USER>\Desktop\meals\backend)
2025-08-14 19:11:38.732 [main] INFO  com.example.meals.MealsApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-14 19:11:39.607 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-14 19:11:39.613 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-14 19:11:39.614 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-14 19:11:39.614 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-08-14 19:11:39.648 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-14 19:11:39.648 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 880 ms
2025-08-14 19:11:40.022 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@5c59a0f7, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@101330ad, org.springframework.security.web.context.SecurityContextHolderFilter@1be52861, org.springframework.security.web.header.HeaderWriterFilter@3be3e76c, org.springframework.security.web.authentication.logout.LogoutFilter@49754e74, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@615439f7, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@632cf7d3, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@625487a6, org.springframework.security.web.session.SessionManagementFilter@77c66a4f, org.springframework.security.web.access.ExceptionTranslationFilter@20bb85b4, org.springframework.security.web.access.intercept.AuthorizationFilter@2becfd4c]
2025-08-14 19:11:40.257 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-14 19:11:40.279 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path ''
2025-08-14 19:11:40.286 [main] INFO  com.example.meals.MealsApplication - Started MealsApplication in 2.081 seconds (process running for 2.761)
