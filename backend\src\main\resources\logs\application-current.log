2025-08-14 19:07:10.576 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-08-14 19:07:10.606 [main] INFO  com.example.meals.MealsApplication - Starting MealsApplication using Java 21.0.8 with PID 12688 (C:\Users\<USER>\Desktop\meals\backend\target\classes started by 21653 in C:\Users\<USER>\Desktop\meals\backend)
2025-08-14 19:07:10.607 [main] INFO  com.example.meals.MealsApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-14 19:07:11.455 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-14 19:07:11.465 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-14 19:07:11.467 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-14 19:07:11.467 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-08-14 19:07:11.499 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-14 19:07:11.499 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 853 ms
2025-08-14 19:07:11.855 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@5bec3e0, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3b57dba4, org.springframework.security.web.context.SecurityContextHolderFilter@20bb85b4, org.springframework.security.web.header.HeaderWriterFilter@71c0b742, org.springframework.security.web.authentication.logout.LogoutFilter@67d8faec, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3be3e76c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6c07ad6b, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@35f7969d, org.springframework.security.web.session.SessionManagementFilter@4703c998, org.springframework.security.web.access.ExceptionTranslationFilter@2abc55c4, org.springframework.security.web.access.intercept.AuthorizationFilter@6b2aafbc]
2025-08-14 19:07:12.084 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-14 19:07:12.095 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path ''
2025-08-14 19:07:12.100 [main] INFO  com.example.meals.MealsApplication - Started MealsApplication in 1.976 seconds (process running for 2.637)
