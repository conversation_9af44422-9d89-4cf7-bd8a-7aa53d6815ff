# 开发环境配置
spring:
  profiles:
    active: dev

# 开发环境日志配置
logging:
  level:
    # MyBatis SQL日志 - 开发环境开启详细日志
    com.example.meals.mapper: DEBUG
    # 根日志级别
    root: INFO
    # Spring框架日志
    org.springframework: INFO
    # 数据库连接池日志
    com.zaxxer.hikari: DEBUG
    # Hibernate SQL日志（如果使用JPA）
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  
  # 日志文件配置
  file:
    # 日志文件路径
    path: src/main/resources/logs
  
  # 日志输出格式
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"

# MyBatis配置
mybatis:
  configuration:
    # 开启SQL日志输出
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
    # 开启详细的SQL参数日志
    log-prefix: "[MyBatis] "
